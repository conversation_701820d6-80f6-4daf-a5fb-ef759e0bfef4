#!/usr/bin/env python3
"""
分析btoa函数原理和Base64解码
"""

import base64
import binascii

class Base64Analyzer:
    """Base64分析器"""
    
    def __init__(self):
        self.live_key = "***********************************************************************************************************"
    
    def analyze_btoa_process(self):
        """分析btoa函数的工作原理"""
        print("🔍 btoa函数原理分析")
        print("=" * 60)
        
        # 步骤1: 原始字符串
        original = self.live_key + ":"
        print(f"步骤1 - 原始字符串:")
        print(f"  {original}")
        
        # 步骤2: 转换为字节
        bytes_data = original.encode('utf-8')
        print(f"\n步骤2 - UTF-8字节:")
        print(f"  {bytes_data}")
        print(f"  字节长度: {len(bytes_data)}")
        
        # 步骤3: Base64编码
        encoded = base64.b64encode(bytes_data).decode('ascii')
        print(f"\n步骤3 - Base64编码:")
        print(f"  {encoded}")
        
        # 步骤4: 添加Basic前缀
        auth_header = "Basic" + encoded  # 注意：这里没有空格，和您的例子一致
        print(f"\n步骤4 - 完整认证头:")
        print(f"  {auth_header}")
        
        # 验证与JavaScript btoa的一致性
        print(f"\n✅ 验证结果:")
        expected = "BasiccmtfbGl2ZV81MUw5c3JLQ29GamtETjR4UEpINzVFQmt6bEFmbVlTMHoybnNDcWE1cWJPaDlwa1prcEVpdEFVSlplYndXNTZlN3lmbWlhOFZrczdONXJXazhlaklGWkI5VDAwRzJ0WVo0c1Y6"
        print(f"  预期结果: {expected}")
        print(f"  实际结果: {auth_header}")
        print(f"  结果一致: {'✅' if auth_header == expected else '❌'}")
        
        return encoded
    
    def analyze_mysterious_header(self):
        """分析神秘的Base64字符串"""
        print(f"\n🔐 神秘Base64字符串分析")
        print("=" * 60)
        
        mysterious = "ZnVuY3Rpb24gdChpKSB7CiAgICBpZiAobltpXSkgcmV0dXJuIG5baV0uZXhwb3J0czsKICAgIHZhciBvID0gKG5baV0gPSB7IGk6IGksIGw6ICExLCBleHBvcnRzOiB7fSB9KTsKICAgIHJldHVybiBlW2ldLmNhbGwoby5leHBvcnRzLCBvLCBvLmV4cG9ydHMsIHQpLCAoby5sID0gITApLCBvLmV4cG9ydHM7CiAgfTo="
        
        print(f"神秘字符串:")
        print(f"  {mysterious}")
        
        try:
            # 解码Base64
            decoded_bytes = base64.b64decode(mysterious)
            decoded_text = decoded_bytes.decode('utf-8')
            
            print(f"\n解码结果:")
            print(f"  原始字节: {decoded_bytes}")
            print(f"  UTF-8文本:")
            print(f"  {decoded_text}")
            
            # 分析代码内容
            print(f"\n🧩 代码分析:")
            if "function" in decoded_text:
                print(f"  ✅ 这是一个JavaScript函数")
                print(f"  函数名: 可能是 't'")
                print(f"  参数: i")
                
                # 格式化显示
                formatted_code = decoded_text.replace(';', ';\n').replace('{', '{\n    ').replace('}', '\n}')
                print(f"\n  格式化后的代码:")
                for i, line in enumerate(formatted_code.split('\n'), 1):
                    if line.strip():
                        print(f"    {i:2d}: {line}")
            
            # 分析功能
            print(f"\n🎯 功能分析:")
            if "exports" in decoded_text and "require" in decoded_text:
                print(f"  ✅ 这是一个模块加载器函数")
                print(f"  用途: 类似于Node.js的require函数")
                print(f"  功能: 加载和缓存模块")
            elif "exports" in decoded_text:
                print(f"  ✅ 这是一个模块系统相关的函数")
                print(f"  用途: 处理模块导出")
            
        except Exception as e:
            print(f"  ❌ 解码失败: {e}")
            
            # 尝试其他解码方式
            try:
                # 尝试URL安全的Base64
                decoded_bytes = base64.urlsafe_b64decode(mysterious + '==')
                decoded_text = decoded_bytes.decode('utf-8')
                print(f"  URL安全Base64解码成功: {decoded_text}")
            except:
                print(f"  所有解码方式都失败")
    
    def compare_encoding_methods(self):
        """对比不同的编码方法"""
        print(f"\n🔧 编码方法对比")
        print("=" * 60)
        
        test_string = self.live_key + ":"
        
        # Python base64
        python_encoded = base64.b64encode(test_string.encode()).decode()
        
        # 手动验证btoa等价性
        print(f"测试字符串: {test_string[:50]}...")
        print(f"Python base64: {python_encoded[:50]}...")
        
        # 验证解码
        try:
            decoded = base64.b64decode(python_encoded).decode()
            print(f"解码验证: {'✅' if decoded == test_string else '❌'}")
        except:
            print(f"解码验证: ❌ 失败")
        
        # 分析字符集
        print(f"\nBase64字符集分析:")
        charset = set(python_encoded)
        print(f"  使用的字符: {sorted(charset)}")
        print(f"  字符数量: {len(charset)}")
        print(f"  是否标准Base64: {'✅' if charset.issubset(set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=')) else '❌'}")
    
    def demonstrate_browser_btoa(self):
        """演示浏览器btoa函数的等价实现"""
        print(f"\n🌐 浏览器btoa函数等价实现")
        print("=" * 60)
        
        def python_btoa(string):
            """Python版本的btoa函数"""
            return base64.b64encode(string.encode('utf-8')).decode('ascii')
        
        test_cases = [
            self.live_key + ":",
            "hello world",
            "测试中文",
            "special!@#$%^&*()"
        ]
        
        for test in test_cases:
            encoded = python_btoa(test)
            print(f"输入: {test[:30]}{'...' if len(test) > 30 else ''}")
            print(f"btoa结果: {encoded}")
            
            # 验证解码
            try:
                decoded = base64.b64decode(encoded).decode('utf-8')
                print(f"解码验证: {'✅' if decoded == test else '❌'}")
            except:
                print(f"解码验证: ❌")
            print()
    
    def analyze_production_key_issue(self):
        """分析生产环境密钥问题"""
        print(f"\n⚠️  生产环境密钥问题分析")
        print("=" * 60)
        
        print(f"问题现状:")
        print(f"  ✅ 测试环境密钥: 仍然有效，可以获取数据")
        print(f"  ❌ 生产环境密钥: 已过期，返回401错误")
        print(f"  🎯 需求: 生产环境的license_code才是真正有效的")
        
        print(f"\n可能的解决方案:")
        print(f"  1. 寻找更新的生产环境密钥")
        print(f"  2. 分析是否有其他API端点")
        print(f"  3. 研究测试环境数据是否可用")
        print(f"  4. 寻找密钥更新机制")
        
        print(f"\n⚖️  法律和道德提醒:")
        print(f"  - 仅用于技术学习目的")
        print(f"  - 不得用于实际破解")
        print(f"  - 尊重服务提供商权益")
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("🎯 Base64编码和解码完整分析")
        print("=" * 80)
        
        # 1. 分析btoa过程
        encoded = self.analyze_btoa_process()
        
        # 2. 分析神秘字符串
        self.analyze_mysterious_header()
        
        # 3. 对比编码方法
        self.compare_encoding_methods()
        
        # 4. 演示btoa等价实现
        self.demonstrate_browser_btoa()
        
        # 5. 分析生产密钥问题
        self.analyze_production_key_issue()
        
        return encoded

def main():
    """主函数"""
    analyzer = Base64Analyzer()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
