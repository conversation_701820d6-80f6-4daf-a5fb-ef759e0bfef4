{"test_info": {"api_key": "sk_test_51LAODbKk4JSg1C413YcQMPYJrC0jYAVdCdXXiAwl2OgqgeTuvGBgerR882PJ0TA4vjn94RQbYPuvdat8T4pGRLdU005DJbwqKt", "credentials": "sk_test_51LAODbKk4JSg1C413YcQMPYJrC0jYAVdCdXXiAwl2OgqgeTuvGBgerR882PJ0TA4vjn94RQbYPuvdat8T4pGRLdU005DJbwqKt:", "base64_encoded": "c2tfdGVzdF81MUxBT0RiS2s0SlNnMUM0MTNZY1FNUFlKckMwallBVmRDZFhYaUF3bDJPZ3FnZVR1dkdCZ2VyUjg4MlBKMFRBNHZqbjk0UlFiWVB1dmRhdDhUNHBHUkxkVTAwNURKYndxS3Q6", "auth_header": "Basic c2tfdGVzdF81MUxBT0RiS2s0SlNnMUM0MTNZY1FNUFlKckMwallBVmRDZFhYaUF3bDJPZ3FnZVR1dkdCZ2VyUjg4MlBKMFRBNHZqbjk0UlFiWVB1dmRhdDhUNHBHUkxkVTAwNURKYndxS3Q6", "key_length": 107, "encoded_length": 144, "header_length": 150}, "live_info": {"api_key": "***********************************************************************************************************", "credentials": "***********************************************************************************************************:", "base64_encoded": "cmtfbGl2ZV81MUw5c3JLQ29GamtETjR4UEpINzVFQmt6bEFmbVlTMHoybnNDcWE1cWJPaDlwa1prcEVpdEFVSlplYndXNTZlN3lmbWlhOFZrczdONXJXazhlaklGWkI5VDAwRzJ0WVo0c1Y6", "auth_header": "Basic cmtfbGl2ZV81MUw5c3JLQ29GamtETjR4UEpINzVFQmt6bEFmbVlTMHoybnNDcWE1cWJPaDlwa1prcEVpdEFVSlplYndXNTZlN3lmbWlhOFZrczdONXJXazhlaklGWkI5VDAwRzJ0WVo0c1Y6", "key_length": 107, "encoded_length": 144, "header_length": 150}, "test_results": {"测试环境": {"status_code": 200, "success": true, "response_size": 10038, "content_type": "application/json", "customers_count": 10, "response_data": {"object": "list", "data": [{"id": "cus_R58V72z39eZJ0W", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "581FE3A8", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "R58V-72z3-9eZJ-0WC8-0523"}, "name": "<PERSON><PERSON>", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_R585qdMZQMrc2a", "object": "customer", "address": {"city": null, "country": "RO", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "54B97BD2", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "R585-qdMZ-QMrc-2aC8-22A5"}, "name": "<PERSON><PERSON>", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_R4NulcAGT6uWba", "object": "customer", "address": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "E1E47407", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {}, "name": "tom", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_R4NaXW3CDDtH4h", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": 1729491042, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "985D7FC0", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {}, "name": "tom", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_R4LTSzfgUK9xNN", "object": "customer", "address": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": 1729483169, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "6A70C68A", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {}, "name": "tom", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_O07FUM9FmpfhzH", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "A29DE77B", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "O07F-UM9F-mpfh-zHC8-27B9"}, "name": "<PERSON>", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_NmF6BANwToYMro", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "D594918D", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "NmF6-BANw-ToYM-roC8-83F8"}, "name": "Liming", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_NluagaHTOvLi36", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "3ACFB5C2", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "Nlua-gaHT-OvLi-36C8-7F07"}, "name": "<PERSON>", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_NlVb3wdtt6IvDA", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "F39C7DDE", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "NlVb-3wdt-t6Iv-DAC8-8F12"}, "name": "Liming", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_NlUqtRF1gYGSPe", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "A9B6CFC0", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "NlUq-tRF1-gYGS-PeC8-9FFB"}, "name": "Liming", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}], "has_more": true, "url": "/v1/customers"}}, "生产环境": {"status_code": 401, "success": false, "response_size": 310, "content_type": "application/json", "error": "{\n  \"error\": {\n    \"code\": \"api_key_expired\",\n    \"doc_url\": \"https://stripe.com/docs/error-codes/api-key-expired\",\n    \"message\": \"Expired API Key provided: rk_live_*********************************************************************************************tYZ4sV\",\n    \"type\": \"invalid_request_error\"\n  }\n}\n"}}}