#!/usr/bin/env python3
"""
对比不同API密钥生成的验证头
专门测试live_key生成的验证头差异
"""

import base64
import requests
import json

class AuthHeaderComparator:
    """验证头对比器"""
    
    def __init__(self):
        # 从renderer解混淆.js中提取的两个密钥
        self.test_key = "sk_test_51LAODbKk4JSg1C413YcQMPYJrC0jYAVdCdXXiAwl2OgqgeTuvGBgerR882PJ0TA4vjn94RQbYPuvdat8T4pGRLdU005DJbwqKt"
        self.live_key = "***********************************************************************************************************"
    
    def generate_auth_header(self, api_key: str) -> dict:
        """生成验证头并返回详细信息"""
        # 步骤1: 添加冒号
        credentials = api_key + ":"
        
        # 步骤2: Base64编码
        encoded_bytes = base64.b64encode(credentials.encode())
        encoded_str = encoded_bytes.decode()
        
        # 步骤3: 构造完整验证头
        auth_header = f"Basic {encoded_str}"
        
        return {
            "api_key": api_key,
            "credentials": credentials,
            "base64_encoded": encoded_str,
            "auth_header": auth_header,
            "key_length": len(api_key),
            "encoded_length": len(encoded_str),
            "header_length": len(auth_header)
        }
    
    def compare_headers(self):
        """对比两个密钥生成的验证头"""
        print("🔍 对比不同API密钥生成的验证头")
        print("=" * 80)
        
        # 生成两个验证头
        test_info = self.generate_auth_header(self.test_key)
        live_info = self.generate_auth_header(self.live_key)
        
        print("📋 详细对比:")
        print("-" * 80)
        
        # 对比API密钥
        print("1. API密钥对比:")
        print(f"   测试密钥: {test_info['api_key']}")
        print(f"   生产密钥: {live_info['api_key']}")
        print(f"   密钥长度: 测试={test_info['key_length']} vs 生产={live_info['key_length']}")
        print(f"   密钥相同: {'✅' if test_info['api_key'] == live_info['api_key'] else '❌'}")
        
        # 对比添加冒号后
        print(f"\n2. 添加冒号后对比:")
        print(f"   测试: {test_info['credentials']}")
        print(f"   生产: {live_info['credentials']}")
        print(f"   相同: {'✅' if test_info['credentials'] == live_info['credentials'] else '❌'}")
        
        # 对比Base64编码
        print(f"\n3. Base64编码对比:")
        print(f"   测试编码: {test_info['base64_encoded']}")
        print(f"   生产编码: {live_info['base64_encoded']}")
        print(f"   编码长度: 测试={test_info['encoded_length']} vs 生产={live_info['encoded_length']}")
        print(f"   编码相同: {'✅' if test_info['base64_encoded'] == live_info['base64_encoded'] else '❌'}")
        
        # 对比最终验证头
        print(f"\n4. 最终验证头对比:")
        print(f"   测试验证头: {test_info['auth_header']}")
        print(f"   生产验证头: {live_info['auth_header']}")
        print(f"   头部长度: 测试={test_info['header_length']} vs 生产={live_info['header_length']}")
        print(f"   验证头相同: {'✅' if test_info['auth_header'] == live_info['auth_header'] else '❌'}")
        
        return test_info, live_info
    
    def test_both_headers(self, test_info: dict, live_info: dict):
        """测试两个验证头的API调用结果"""
        print(f"\n🧪 API调用测试对比")
        print("=" * 80)
        
        results = {}
        
        for key_type, info in [("测试环境", test_info), ("生产环境", live_info)]:
            print(f"\n🔑 {key_type}密钥测试:")
            print(f"验证头: {info['auth_header'][:50]}...")
            
            headers = {
                "Authorization": info['auth_header'],
                "User-Agent": "AuthHeaderComparator/1.0"
            }
            
            try:
                response = requests.get(
                    "https://api.stripe.com/v1/customers",
                    headers=headers,
                    timeout=30
                )
                
                result = {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_size": len(response.content),
                    "content_type": response.headers.get("Content-Type", "unknown")
                }
                
                print(f"  状态码: {result['status_code']}")
                print(f"  响应大小: {result['response_size']} 字节")
                print(f"  内容类型: {result['content_type']}")
                
                if result['success']:
                    try:
                        data = response.json()
                        result['customers_count'] = len(data.get('data', []))
                        result['response_data'] = data
                        print(f"  ✅ 成功！客户数量: {result['customers_count']}")
                    except:
                        print(f"  ⚠️ 响应解析失败")
                else:
                    result['error'] = response.text
                    print(f"  ❌ 失败！错误: {response.text[:100]}...")
                
                results[key_type] = result
                
            except Exception as e:
                print(f"  ❌ 请求异常: {e}")
                results[key_type] = {"success": False, "error": str(e)}
        
        return results
    
    def analyze_differences(self, test_info: dict, live_info: dict):
        """分析两个验证头的差异"""
        print(f"\n📊 差异分析")
        print("=" * 80)
        
        # 分析密钥前缀
        test_prefix = test_info['api_key'][:8]
        live_prefix = live_info['api_key'][:8]
        
        print(f"密钥前缀分析:")
        print(f"  测试密钥前缀: {test_prefix}")
        print(f"  生产密钥前缀: {live_prefix}")
        print(f"  前缀不同: {'✅' if test_prefix != live_prefix else '❌'}")
        
        # 分析Base64编码差异
        test_b64 = test_info['base64_encoded']
        live_b64 = live_info['base64_encoded']
        
        print(f"\nBase64编码差异:")
        print(f"  测试编码前20字符: {test_b64[:20]}")
        print(f"  生产编码前20字符: {live_b64[:20]}")
        
        # 找出第一个不同的字符位置
        diff_pos = -1
        for i, (t, l) in enumerate(zip(test_b64, live_b64)):
            if t != l:
                diff_pos = i
                break
        
        if diff_pos >= 0:
            print(f"  第一个差异位置: 第{diff_pos + 1}个字符")
            print(f"  测试: '{test_b64[diff_pos]}' vs 生产: '{live_b64[diff_pos]}'")
        else:
            print(f"  编码完全相同")
        
        # 验证解码
        try:
            test_decoded = base64.b64decode(test_b64).decode()
            live_decoded = base64.b64decode(live_b64).decode()
            
            print(f"\n解码验证:")
            print(f"  测试解码: {test_decoded == test_info['credentials']}")
            print(f"  生产解码: {live_decoded == live_info['credentials']}")
        except Exception as e:
            print(f"\n解码验证失败: {e}")
    
    def generate_usage_examples(self, test_info: dict, live_info: dict, test_results: dict):
        """生成使用示例"""
        print(f"\n💻 使用示例代码")
        print("=" * 80)
        
        # 测试环境示例
        if test_results.get("测试环境", {}).get("success"):
            print(f"# 测试环境 - 可用")
            print(f'headers_test = {{"Authorization": "{test_info["auth_header"]}"}}')
            print(f'response = requests.get("https://api.stripe.com/v1/customers", headers=headers_test)')
            print(f'# 预期结果: HTTP 200, 包含客户数据')
        
        print(f"\n# 生产环境 - {'可用' if test_results.get('生产环境', {}).get('success') else '已失效'}")
        print(f'headers_live = {{"Authorization": "{live_info["auth_header"]}"}}')
        print(f'response = requests.get("https://api.stripe.com/v1/customers", headers=headers_live)')
        
        if test_results.get("生产环境", {}).get("success"):
            print(f'# 预期结果: HTTP 200, 包含客户数据')
        else:
            print(f'# 预期结果: HTTP 401, API密钥已过期')
        
        print(f"\n# 通用函数")
        print(f"def create_auth_header(api_key):")
        print(f'    credentials = api_key + ":"')
        print(f'    encoded = base64.b64encode(credentials.encode()).decode()')
        print(f'    return f"Basic {{encoded}}"')
    
    def run_complete_comparison(self):
        """运行完整对比"""
        print("🎯 API密钥验证头完整对比测试")
        print("=" * 100)
        
        # 1. 生成和对比验证头
        test_info, live_info = self.compare_headers()
        
        # 2. 测试API调用
        test_results = self.test_both_headers(test_info, live_info)
        
        # 3. 分析差异
        self.analyze_differences(test_info, live_info)
        
        # 4. 生成使用示例
        self.generate_usage_examples(test_info, live_info, test_results)
        
        # 5. 总结
        print(f"\n📋 总结")
        print("=" * 50)
        print(f"测试密钥验证头: {'✅ 有效' if test_results.get('测试环境', {}).get('success') else '❌ 无效'}")
        print(f"生产密钥验证头: {'✅ 有效' if test_results.get('生产环境', {}).get('success') else '❌ 无效'}")
        print(f"验证头是否相同: {'✅ 相同' if test_info['auth_header'] == live_info['auth_header'] else '❌ 不同'}")
        
        return {
            "test_info": test_info,
            "live_info": live_info,
            "test_results": test_results
        }

def main():
    """主函数"""
    comparator = AuthHeaderComparator()
    results = comparator.run_complete_comparison()
    
    # 保存结果
    with open("auth_header_comparison.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n💾 详细对比结果已保存到 auth_header_comparison.json")

if __name__ == "__main__":
    main()
