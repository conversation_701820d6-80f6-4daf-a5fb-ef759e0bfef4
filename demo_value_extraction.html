<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Greenhub值提取技术演示</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .code-block {
            background: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .highlight {
            background: #264f78;
            padding: 2px 4px;
            border-radius: 3px;
        }
        button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #1177bb;
        }
        .result {
            background: #0f3460;
            border-left: 4px solid #007acc;
            padding: 10px;
            margin: 10px 0;
        }
        .warning {
            background: #663d00;
            border-left: 4px solid #ff8c00;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Greenhub值提取技术演示</h1>
        
        <h2>📋 技术原理</h2>
        <p>这个演示展示了如何在Electron应用中提取运行时变量值的巧妙方法。</p>
        
        <h3>1. 模拟环境变量判断</h3>
        <div class="code-block">
            <span class="highlight">var n = "dev" === process.env.APP_BUILD_TYPE ? "sk_test_..." : "rk_live_...";</span>
        </div>
        
        <h3>2. 原始API函数</h3>
        <div class="code-block">
STRIPE_GET_ACTIVE_CODE: function(e) {
    return t({
        url: "https://api.stripe.com/v1/customers/" + e,
        method: "get",
        headers: { Authorization: <span class="highlight">"Basic " + btoa(n + ":")</span> }
    })
}
        </div>
        
        <h3>3. 修改后的函数（添加值提取功能）</h3>
        <div class="code-block">
STRIPE_GET_ACTIVE_CODE: function(e) {
    <span class="highlight">// 新增代码 - 值提取
    var temp = window.confirm("Click ok to copy");
    if (temp) {
        var textarea = document.createElement("textarea");
        textarea.value = "Basic" + btoa(n+":");
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand("copy");
        document.body.removeChild(textarea);
    }</span>
    
    // 原始代码
    return t({...})
}
        </div>
        
        <h2>🧪 实际演示</h2>
        
        <h3>模拟不同环境的API密钥</h3>
        <button onclick="simulateTestEnv()">测试环境 (dev)</button>
        <button onclick="simulateLiveEnv()">生产环境 (live)</button>
        
        <h3>演示值提取过程</h3>
        <button onclick="demonstrateExtraction()">触发值提取</button>
        <button onclick="showAuthHeader()">显示认证头构造</button>
        
        <div id="results"></div>
        
        <div class="warning">
            <strong>⚠️ 重要提醒：</strong>
            <ul>
                <li>这个技术仅用于学习和研究目的</li>
                <li>实际的API密钥已经失效</li>
                <li>不得用于任何非法用途</li>
                <li>document.execCommand已被废弃，现代浏览器推荐使用Clipboard API</li>
            </ul>
        </div>
        
        <h2>🔧 技术要点解析</h2>
        <div class="code-block">
<strong>1. 弹窗确认：</strong>
window.confirm("Click ok to copy")
// 返回 true/false，用户交互确认

<strong>2. 动态DOM操作：</strong>
var textarea = document.createElement("textarea");
textarea.value = "要复制的内容";
document.body.appendChild(textarea);

<strong>3. 文本选择：</strong>
textarea.select();
// 选中textarea中的所有文本

<strong>4. 复制到剪贴板：</strong>
document.execCommand("copy");
// 将选中的文本复制到系统剪贴板

<strong>5. 清理DOM：</strong>
document.body.removeChild(textarea);
// 移除临时创建的元素
        </div>
    </div>

    <script>
        // 模拟API密钥
        const testKey = "sk_test_51LAODbKk4JSg1C413YcQMPYJrC0jYAVdCdXXiAwl2OgqgeTuvGBgerR882PJ0TA4vjn94RQbYPuvdat8T4pGRLdU005DJbwqKt";
        const liveKey = "***********************************************************************************************************";
        
        let currentEnv = "live";
        let currentKey = liveKey;
        
        function updateResults(content) {
            document.getElementById('results').innerHTML = content;
        }
        
        function simulateTestEnv() {
            currentEnv = "dev";
            currentKey = testKey;
            updateResults(`
                <div class="result">
                    <h4>🧪 切换到测试环境</h4>
                    <p><strong>环境：</strong> dev</p>
                    <p><strong>API密钥：</strong> ${currentKey.substring(0, 30)}...</p>
                    <p><strong>密钥类型：</strong> sk_test_ (测试密钥)</p>
                </div>
            `);
        }
        
        function simulateLiveEnv() {
            currentEnv = "live";
            currentKey = liveKey;
            updateResults(`
                <div class="result">
                    <h4>🚀 切换到生产环境</h4>
                    <p><strong>环境：</strong> live</p>
                    <p><strong>API密钥：</strong> ${currentKey.substring(0, 30)}...</p>
                    <p><strong>密钥类型：</strong> rk_live_ (生产密钥)</p>
                </div>
            `);
        }
        
        function demonstrateExtraction() {
            // 模拟原文章中的值提取过程
            const confirmed = window.confirm("点击确定来复制Authorization头到剪贴板");
            
            if (confirmed) {
                const credentials = currentKey + ":";
                const encoded = btoa(credentials);
                const authHeader = "Basic " + encoded;
                
                // 创建临时textarea
                const textarea = document.createElement("textarea");
                textarea.value = authHeader;
                document.body.appendChild(textarea);
                textarea.select();
                
                try {
                    // 尝试复制
                    const successful = document.execCommand('copy');
                    document.body.removeChild(textarea);
                    
                    updateResults(`
                        <div class="result">
                            <h4>✅ 值提取成功！</h4>
                            <p><strong>提取的Authorization头：</strong></p>
                            <div class="code-block">${authHeader.substring(0, 50)}...</div>
                            <p><strong>状态：</strong> ${successful ? '已复制到剪贴板' : '复制失败'}</p>
                            <p><strong>原理：</strong> 通过动态创建textarea元素，设置值后选中并复制</p>
                        </div>
                    `);
                } catch (err) {
                    document.body.removeChild(textarea);
                    updateResults(`
                        <div class="warning">
                            <h4>⚠️ 复制失败</h4>
                            <p>现代浏览器可能阻止了document.execCommand</p>
                            <p>但演示了技术原理</p>
                        </div>
                    `);
                }
            } else {
                updateResults(`
                    <div class="result">
                        <h4>❌ 用户取消操作</h4>
                        <p>这模拟了用户点击"取消"的情况</p>
                    </div>
                `);
            }
        }
        
        function showAuthHeader() {
            const credentials = currentKey + ":";
            const encoded = btoa(credentials);
            const authHeader = "Basic " + encoded;
            
            updateResults(`
                <div class="result">
                    <h4>🔐 认证头构造过程</h4>
                    <p><strong>步骤1 - 原始密钥：</strong></p>
                    <div class="code-block">${currentKey}</div>
                    
                    <p><strong>步骤2 - 添加冒号：</strong></p>
                    <div class="code-block">${credentials}</div>
                    
                    <p><strong>步骤3 - Base64编码：</strong></p>
                    <div class="code-block">${encoded}</div>
                    
                    <p><strong>步骤4 - 完整认证头：</strong></p>
                    <div class="code-block">${authHeader}</div>
                    
                    <p><strong>JavaScript实现：</strong></p>
                    <div class="code-block">"Basic " + btoa("${currentKey}:")</div>
                </div>
            `);
        }
        
        // 初始化显示
        simulateLiveEnv();
    </script>
</body>
</html>
