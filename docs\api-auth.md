# API认证机制详解

## 基础认证（Basic Authentication）

### 原理说明

Basic Authentication是HTTP协议中最简单的认证方式，通过在请求头中包含用户名和密码来验证身份。

### 认证流程

1. **客户端**：将用户名和密码用冒号连接
2. **编码**：使用Base64编码处理
3. **发送**：在HTTP头中添加Authorization字段
4. **服务器**：解码并验证凭据

### 格式规范

```
Authorization: Basic <base64(username:password)>
```

## Base64编码详解

### 什么是Base64？

Base64是一种基于64个可打印字符来表示二进制数据的编码方法，常用于在HTTP环境下传输二进制数据。

### 编码字符集

```
ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/
```

### 编码过程

1. 将输入数据按3字节分组
2. 每组24位分成4个6位块
3. 每个6位块对应一个Base64字符
4. 不足位数用'='填充

### 示例演示

**原始字符串**：`sk_test_123:`
**Base64编码**：`c2tfdGVzdF8xMjM6`

## Greenhub案例分析

### 认证头构造

根据源码分析，Greenhub使用以下方式构造认证头：

```javascript
// 原始代码
var n = "dev" === process.env.APP_BUILD_TYPE ? "sk_test_..." : "rk_live_...";
var authHeader = "Basic " + btoa(n + ":");
```

### 关键要素分解

1. **API密钥获取**
   ```javascript
   // 三目运算符判断环境
   var apiKey = isDev ? testKey : liveKey;
   ```

2. **格式化处理**
   ```javascript
   // 添加冒号后缀
   var credentials = apiKey + ":";
   ```

3. **Base64编码**
   ```javascript
   // btoa函数进行编码
   var encoded = btoa(credentials);
   ```

4. **构造认证头**
   ```javascript
   // 添加Basic前缀
   var authHeader = "Basic " + encoded;
   ```

## Python实现

### 基础实现

```python
import base64
import requests

def create_auth_header(api_key):
    """创建Basic认证头"""
    credentials = f"{api_key}:"
    encoded = base64.b64encode(credentials.encode()).decode()
    return f"Basic {encoded}"

def make_api_request(api_key, endpoint):
    """发起API请求"""
    headers = {
        "Authorization": create_auth_header(api_key)
    }
    response = requests.get(endpoint, headers=headers)
    return response.json()
```

### 完整示例

```python
import base64
import requests
import json
from typing import Dict, List, Optional

class StripeAPIClient:
    """Stripe API客户端（学习用途）"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.stripe.com/v1"
        self.session = requests.Session()
        self._setup_auth()
    
    def _setup_auth(self):
        """设置认证头"""
        credentials = f"{self.api_key}:"
        encoded = base64.b64encode(credentials.encode()).decode()
        self.session.headers.update({
            "Authorization": f"Basic {encoded}",
            "Content-Type": "application/x-www-form-urlencoded"
        })
    
    def get_customers(self) -> Dict:
        """获取客户列表"""
        url = f"{self.base_url}/customers"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def extract_license_codes(self, customers_data: Dict) -> List[str]:
        """提取激活码"""
        license_codes = []
        for customer in customers_data.get('data', []):
            metadata = customer.get('metadata', {})
            if 'license_code' in metadata:
                license_codes.append(metadata['license_code'])
        return license_codes
```

## 安全考虑

### API密钥安全

1. **密钥类型**
   - `sk_test_` - 测试环境密钥
   - `sk_live_` - 生产环境密钥
   - `pk_` - 公开密钥

2. **安全存储**
   - 环境变量
   - 配置文件加密
   - 密钥管理服务

3. **权限控制**
   - 最小权限原则
   - 定期轮换密钥
   - 监控异常访问

### 传输安全

1. **HTTPS加密**：确保传输过程安全
2. **证书验证**：防止中间人攻击
3. **请求签名**：防止请求篡改

## 常见问题

### 编码问题

```python
# 错误示例
auth = base64.b64encode("key:")  # 返回bytes对象

# 正确示例
auth = base64.b64encode("key:".encode()).decode()  # 返回字符串
```

### 格式问题

```python
# 错误格式
headers = {"Authorization": encoded}

# 正确格式
headers = {"Authorization": f"Basic {encoded}"}
```

### 环境判断

```python
import os

def get_api_key():
    """根据环境获取API密钥"""
    env = os.getenv('APP_BUILD_TYPE', 'production')
    if env == 'dev':
        return os.getenv('STRIPE_TEST_KEY')
    else:
        return os.getenv('STRIPE_LIVE_KEY')
```

## 实践练习

### 练习1：编码解码

```python
def practice_base64():
    """Base64编码练习"""
    test_strings = [
        "sk_test_123:",
        "rk_live_456:",
        "username:password"
    ]
    
    for s in test_strings:
        encoded = base64.b64encode(s.encode()).decode()
        decoded = base64.b64decode(encoded).decode()
        print(f"原文: {s}")
        print(f"编码: {encoded}")
        print(f"解码: {decoded}")
        print("-" * 30)
```

### 练习2：认证头构造

```python
def practice_auth_header():
    """认证头构造练习"""
    api_keys = [
        "sk_test_51L9srKCoFjkDN4xPkotK6WGGPqfwkgwtE6I1q1TE9kvKsgK7JT92NhiAGxjJx44z7GfpsSXY6kiMY2M1VZHEj2YV00n4OKzTJX",
        "rk_live_example_key"
    ]
    
    for key in api_keys:
        auth_header = create_auth_header(key)
        print(f"API Key: {key[:20]}...")
        print(f"Auth Header: {auth_header}")
        print("-" * 50)
```

## 法律和道德提醒

⚠️ **重要提醒**：
- 本文档仅用于技术学习
- 不得用于破解商业API
- 尊重服务提供商的使用条款
- 遵守相关法律法规

---

**下一步**：学习Python网络请求实现
