{"name": "simple-electron-demo", "version": "1.0.0", "description": "简单的Electron应用演示", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "author": "Greenhub Learning Project", "license": "MIT", "devDependencies": {"electron": "^22.0.0", "electron-builder": "^23.6.0"}, "build": {"appId": "com.example.simple-electron", "productName": "Simple Electron Demo", "directories": {"output": "dist"}, "files": ["main.js", "renderer.js", "index.html", "package.json"], "win": {"target": "nsis"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}}}