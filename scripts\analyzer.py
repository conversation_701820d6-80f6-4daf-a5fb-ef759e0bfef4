#!/usr/bin/env python3
"""
Greenhub分析工具
用于学习和分析Electron应用的API调用机制
"""

import os
import sys
import json
import base64
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Any
import requests
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class GreenhubAnalyzer:
    """Greenhub分析器（仅用于学习目的）"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config = self._load_config(config_file)
        self.session = requests.Session()
        self._setup_logging()
    
    def _load_config(self, config_file: Optional[str]) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "api_base_url": "https://api.stripe.com/v1",
            "timeout": 30,
            "max_retries": 3,
            "log_level": "INFO"
        }
        
        if config_file and Path(config_file).exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def _setup_logging(self):
        """设置日志"""
        log_level = self.config.get("log_level", "INFO")
        logger.remove()
        logger.add(sys.stderr, level=log_level, 
                  format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                         "<level>{level: <8}</level> | "
                         "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                         "<level>{message}</level>")
    
    def create_auth_header(self, api_key: str) -> str:
        """创建Basic认证头"""
        if not api_key:
            raise ValueError("API密钥不能为空")
        
        # 模拟Greenhub的认证头构造方式
        credentials = f"{api_key}:"
        encoded = base64.b64encode(credentials.encode()).decode()
        auth_header = f"Basic {encoded}"
        
        logger.debug(f"API Key: {api_key[:20]}...")
        logger.debug(f"Auth Header: {auth_header}")
        
        return auth_header
    
    def analyze_api_key(self, api_key: str) -> Dict[str, Any]:
        """分析API密钥"""
        analysis = {
            "key": api_key,
            "type": "unknown",
            "environment": "unknown",
            "valid_format": False
        }
        
        if api_key.startswith("sk_test_"):
            analysis["type"] = "secret"
            analysis["environment"] = "test"
            analysis["valid_format"] = True
        elif api_key.startswith("sk_live_"):
            analysis["type"] = "secret"
            analysis["environment"] = "live"
            analysis["valid_format"] = True
        elif api_key.startswith("pk_test_"):
            analysis["type"] = "publishable"
            analysis["environment"] = "test"
            analysis["valid_format"] = True
        elif api_key.startswith("pk_live_"):
            analysis["type"] = "publishable"
            analysis["environment"] = "live"
            analysis["valid_format"] = True
        elif api_key.startswith("rk_live_"):
            analysis["type"] = "restricted"
            analysis["environment"] = "live"
            analysis["valid_format"] = True
        
        return analysis
    
    def test_api_connection(self, api_key: str) -> Dict[str, Any]:
        """测试API连接（仅用于学习）"""
        logger.info("测试API连接...")
        
        # 分析API密钥
        key_analysis = self.analyze_api_key(api_key)
        logger.info(f"API密钥类型: {key_analysis['type']}")
        logger.info(f"环境: {key_analysis['environment']}")
        
        if not key_analysis["valid_format"]:
            return {
                "success": False,
                "error": "API密钥格式无效",
                "analysis": key_analysis
            }
        
        # 构造认证头
        try:
            auth_header = self.create_auth_header(api_key)
            headers = {
                "Authorization": auth_header,
                "User-Agent": "GreenhubAnalyzer/1.0 (Learning Purpose)"
            }
            
            # 测试连接
            url = f"{self.config['api_base_url']}/customers"
            logger.debug(f"请求URL: {url}")
            
            response = self.session.get(
                url, 
                headers=headers, 
                timeout=self.config["timeout"]
            )
            
            result = {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "analysis": key_analysis
            }
            
            if response.status_code == 200:
                data = response.json()
                result["data"] = data
                logger.info("API连接测试成功")
            else:
                result["error"] = response.text
                logger.warning(f"API连接失败: {response.status_code}")
            
            return result
            
        except Exception as e:
            logger.error(f"API连接测试异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis": key_analysis
            }
    
    def extract_license_codes(self, customers_data: Dict) -> List[str]:
        """提取激活码（仅用于学习分析）"""
        license_codes = []
        
        if not isinstance(customers_data, dict):
            logger.warning("客户数据格式无效")
            return license_codes
        
        customers = customers_data.get('data', [])
        logger.info(f"找到 {len(customers)} 个客户记录")
        
        for i, customer in enumerate(customers):
            metadata = customer.get('metadata', {})
            if 'license_code' in metadata:
                code = metadata['license_code']
                license_codes.append(code)
                logger.debug(f"客户 {i+1}: 找到激活码 {code}")
        
        logger.info(f"总共提取到 {len(license_codes)} 个激活码")
        return license_codes
    
    def demo_analysis(self, api_key: str) -> Dict[str, Any]:
        """演示分析过程"""
        logger.info("开始演示分析过程...")
        
        # 测试API连接
        connection_result = self.test_api_connection(api_key)
        
        if not connection_result["success"]:
            logger.error("API连接失败，无法继续分析")
            return connection_result
        
        # 提取激活码
        customers_data = connection_result.get("data", {})
        license_codes = self.extract_license_codes(customers_data)
        
        result = {
            "success": True,
            "api_analysis": connection_result["analysis"],
            "customers_count": len(customers_data.get('data', [])),
            "license_codes": license_codes,
            "license_codes_count": len(license_codes)
        }
        
        logger.info("分析完成")
        return result

def create_sample_config():
    """创建示例配置文件"""
    config = {
        "api_base_url": "https://api.stripe.com/v1",
        "timeout": 30,
        "max_retries": 3,
        "log_level": "INFO",
        "demo_mode": True
    }
    
    config_file = project_root / "config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 示例配置文件已创建: {config_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Greenhub分析工具（仅用于学习目的）",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python analyzer.py --demo sk_test_example_key
  python analyzer.py --analyze-key sk_test_example_key
  python analyzer.py --create-config
  
注意: 本工具仅用于技术学习，请勿用于任何非法用途！
        """
    )
    
    parser.add_argument("--demo", metavar="API_KEY",
                       help="运行完整演示分析")
    parser.add_argument("--analyze-key", metavar="API_KEY",
                       help="分析API密钥格式")
    parser.add_argument("--test-connection", metavar="API_KEY",
                       help="测试API连接")
    parser.add_argument("--create-config", action="store_true",
                       help="创建示例配置文件")
    parser.add_argument("--config", metavar="FILE",
                       help="指定配置文件路径")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="详细输出")
    
    args = parser.parse_args()
    
    if args.create_config:
        create_sample_config()
        return
    
    if not any([args.demo, args.analyze_key, args.test_connection]):
        parser.print_help()
        return
    
    # 创建分析器
    analyzer = GreenhubAnalyzer(args.config)
    
    if args.verbose:
        analyzer.config["log_level"] = "DEBUG"
        analyzer._setup_logging()
    
    try:
        if args.demo:
            result = analyzer.demo_analysis(args.demo)
            print("\n📊 分析结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        
        elif args.analyze_key:
            result = analyzer.analyze_api_key(args.analyze_key)
            print("\n🔍 API密钥分析:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        
        elif args.test_connection:
            result = analyzer.test_api_connection(args.test_connection)
            print("\n🌐 连接测试结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
    
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
