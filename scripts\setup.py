#!/usr/bin/env python3
"""
环境配置脚本
用于自动配置Greenhub学习项目的开发环境
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
from typing import List, Tuple

class EnvironmentSetup:
    """环境配置类"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.project_root = Path(__file__).parent.parent
        self.requirements_file = self.project_root / "requirements.txt"
        
    def check_python_version(self) -> bool:
        """检查Python版本"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python版本过低，需要Python 3.8+")
            return False
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def check_node_version(self) -> bool:
        """检查Node.js版本"""
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ Node.js版本: {version}")
                return True
        except FileNotFoundError:
            pass
        
        print("❌ 未找到Node.js，请安装Node.js 16+")
        return False
    
    def install_python_dependencies(self) -> bool:
        """安装Python依赖"""
        if not self.requirements_file.exists():
            print("❌ 未找到requirements.txt文件")
            return False
        
        try:
            print("📦 安装Python依赖包...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 
                          str(self.requirements_file)], check=True)
            print("✅ Python依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Python依赖安装失败: {e}")
            return False
    
    def install_node_dependencies(self) -> bool:
        """安装Node.js依赖"""
        package_json = self.project_root / "package.json"
        if not package_json.exists():
            # 创建基础package.json
            self.create_package_json()
        
        try:
            print("📦 安装Node.js依赖包...")
            subprocess.run(['npm', 'install'], 
                          cwd=self.project_root, check=True)
            print("✅ Node.js依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Node.js依赖安装失败: {e}")
            return False
    
    def create_package_json(self):
        """创建package.json文件"""
        package_content = {
            "name": "greenhub-learning",
            "version": "1.0.0",
            "description": "Greenhub技术学习项目",
            "main": "index.js",
            "scripts": {
                "test": "echo \"Error: no test specified\" && exit 1"
            },
            "devDependencies": {
                "asar": "^3.2.0",
                "electron": "^22.0.0",
                "electron-builder": "^23.6.0"
            },
            "dependencies": {
                "jsbeautifier": "^1.14.7",
                "esprima": "^4.0.1"
            }
        }
        
        import json
        with open(self.project_root / "package.json", 'w', encoding='utf-8') as f:
            json.dump(package_content, f, indent=2, ensure_ascii=False)
    
    def setup_7zip_plugin(self) -> bool:
        """设置7-zip插件"""
        print("🔧 7-zip .asar插件配置说明:")
        print("1. 下载插件: https://www.tc4shell.com/en/7zip/asar")
        print("2. 解压得到asar.dll文件")
        print("3. 复制到7-zip安装目录的Formats文件夹")
        print("4. 重启7-zip即可支持.asar文件")
        
        if self.system == "windows":
            possible_paths = [
                "C:\\Program Files\\7-Zip\\Formats",
                "C:\\Program Files (x86)\\7-Zip\\Formats"
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    print(f"💡 7-zip Formats目录: {path}")
                    break
        
        return True
    
    def create_project_structure(self) -> bool:
        """创建项目目录结构"""
        directories = [
            "docs",
            "tools/asar-extractor",
            "tools/js-analyzer", 
            "tools/api-tester",
            "examples/simple-electron",
            "examples/auth-demo",
            "examples/network-requests",
            "scripts"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
        
        print("✅ 项目目录结构创建完成")
        return True
    
    def create_env_file(self) -> bool:
        """创建环境配置文件"""
        env_content = """# Greenhub学习项目环境配置

# 开发模式
APP_BUILD_TYPE=dev

# API配置（仅用于学习，请勿使用真实密钥）
STRIPE_TEST_KEY=sk_test_example_key_for_learning_only
STRIPE_LIVE_KEY=rk_live_example_key_for_learning_only

# 日志级别
LOG_LEVEL=INFO

# 项目配置
PROJECT_NAME=greenhub-learning
VERSION=1.0.0
"""
        
        env_file = self.project_root / ".env.example"
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("✅ 环境配置文件创建完成")
        print("💡 请复制.env.example为.env并配置实际参数")
        return True
    
    def run_setup(self) -> bool:
        """运行完整配置"""
        print("🚀 开始配置Greenhub学习环境...\n")
        
        steps = [
            ("检查Python版本", self.check_python_version),
            ("检查Node.js版本", self.check_node_version),
            ("创建项目结构", self.create_project_structure),
            ("安装Python依赖", self.install_python_dependencies),
            ("安装Node.js依赖", self.install_node_dependencies),
            ("配置7-zip插件", self.setup_7zip_plugin),
            ("创建环境文件", self.create_env_file),
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            if step_func():
                success_count += 1
            else:
                print(f"⚠️  {step_name}失败，但可以继续")
        
        print(f"\n🎉 环境配置完成！成功执行 {success_count}/{len(steps)} 个步骤")
        
        if success_count == len(steps):
            print("\n✅ 所有配置项都已成功完成！")
            print("🎯 现在可以开始学习Greenhub技术了")
        else:
            print("\n⚠️  部分配置项需要手动完成，请查看上述说明")
        
        print("\n📚 下一步:")
        print("1. 阅读 docs/ 目录下的学习文档")
        print("2. 运行 python scripts/analyzer.py --help 查看工具使用方法")
        print("3. 查看 examples/ 目录下的示例代码")
        
        return success_count >= len(steps) - 2  # 允许2个步骤失败

def main():
    """主函数"""
    setup = EnvironmentSetup()
    success = setup.run_setup()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
