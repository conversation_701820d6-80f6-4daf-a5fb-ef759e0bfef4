{"test_timestamp": 1752948168.277206, "api_tests": [{"key_type": "测试环境", "api_key": "sk_test_51LAODbKk4JSg1C413YcQM...", "status_code": 200, "success": true, "response_size": 10038, "headers": {"Server": "nginx", "Date": "Sat, 19 Jul 2025 18:02:52 GMT", "Content-Type": "application/json", "Content-Length": "10038", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=0IuFdaPI1xHxqafI9dTNkQbufJbwPsuo_cqsQuTZRrORiJoeLsQaFlPZXKQECcuA4zu4wRy5wu7osEtM", "Request-Id": "req_mbhVROQt58c2mq", "Stripe-Version": "2020-08-27", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHIJ", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "data": {"object": "list", "data": [{"id": "cus_R58V72z39eZJ0W", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "581FE3A8", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "R58V-72z3-9eZJ-0WC8-0523"}, "name": "<PERSON><PERSON>", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_R585qdMZQMrc2a", "object": "customer", "address": {"city": null, "country": "RO", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "54B97BD2", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "R585-qdMZ-QMrc-2aC8-22A5"}, "name": "<PERSON><PERSON>", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_R4NulcAGT6uWba", "object": "customer", "address": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "E1E47407", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {}, "name": "tom", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_R4NaXW3CDDtH4h", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": 1729491042, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "985D7FC0", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {}, "name": "tom", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_R4LTSzfgUK9xNN", "object": "customer", "address": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": 1729483169, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "6A70C68A", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {}, "name": "tom", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_O07FUM9FmpfhzH", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "A29DE77B", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "O07F-UM9F-mpfh-zHC8-27B9"}, "name": "<PERSON>", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_NmF6BANwToYMro", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "D594918D", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "NmF6-BANw-ToYM-roC8-83F8"}, "name": "Liming", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_NluagaHTOvLi36", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "3ACFB5C2", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "Nlua-gaHT-OvLi-36C8-7F07"}, "name": "<PERSON>", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_NlVb3wdtt6IvDA", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "F39C7DDE", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "NlVb-3wdt-t6Iv-DAC8-8F12"}, "name": "Liming", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, {"id": "cus_NlUqtRF1gYGSPe", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "A9B6CFC0", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "NlUq-tRF1-gYGS-PeC8-9FFB"}, "name": "Liming", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}], "has_more": true, "url": "/v1/customers"}, "customers_count": 10, "customer_test": {"customer_id": "cus_R58V72z39eZJ0W", "status_code": 200, "success": true, "customer_data": {"id": "cus_R58V72z39eZJ0W", "object": "customer", "address": {"city": null, "country": "CN", "line1": null, "line2": null, "postal_code": null, "state": null}, "balance": 0, "created": **********, "currency": "usd", "default_currency": "usd", "default_source": null, "delinquent": false, "description": null, "discount": null, "email": "<EMAIL>", "invoice_prefix": "581FE3A8", "invoice_settings": {"custom_fields": null, "default_payment_method": null, "footer": null, "rendering_options": null}, "livemode": false, "metadata": {"license_code": "R58V-72z3-9eZJ-0WC8-0523"}, "name": "<PERSON><PERSON>", "next_invoice_sequence": 1, "phone": null, "preferred_locales": ["zh-CN"], "shipping": null, "tax_exempt": "none", "test_clock": null}, "license_code": "R58V-72z3-9eZJ-0WC8-0523"}}, {"key_type": "生产环境", "api_key": "******************************...", "status_code": 401, "success": false, "response_size": 310, "headers": {"Server": "nginx", "Date": "Sat, 19 Jul 2025 18:02:53 GMT", "Content-Type": "application/json", "Content-Length": "310", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=0IuFdaPI1xHxqafI9dTNkQbufJbwPsuo_cqsQuTZRrORiJoeLsQaFlPZXKQECcuA4zu4wRy5wu7osEtM", "Stripe-Version": "2025-06-30.basil", "Vary": "Origin", "Www-Authenticate": "Basic realm=\"Stripe\"", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "livemode", "X-Wc": "ABGHIJ", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "error": "{\n  \"error\": {\n    \"code\": \"api_key_expired\",\n    \"doc_url\": \"https://stripe.com/docs/error-codes/api-key-expired\",\n    \"message\": \"Expired API Key provided: rk_live_*********************************************************************************************tYZ4sV\",\n    \"type\": \"invalid_request_error\"\n  }\n}\n"}], "license_codes": ["R58V-72z3-9eZJ-0WC8-0523", "R585-qdMZ-QMrc-2aC8-22A5", "O07F-UM9F-mpfh-zHC8-27B9", "NmF6-BANw-ToYM-roC8-83F8", "Nlua-gaHT-OvLi-36C8-7F07", "NlVb-3wdt-t6Iv-DAC8-8F12", "NlUq-tRF1-gYGS-PeC8-9FFB"], "summary": {"total_tests": 2, "successful_tests": 1, "total_license_codes": 7, "test_success_rate": 0.5}}